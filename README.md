# Analysis Worker Service

🧠 **AI-Powered Persona Analysis Worker** untuk ATMA Backend Platform

## Overview

Analysis Worker adalah background service yang memproses analisis psikometri menggunakan Google Gemini. Service ini menerima jobs dari Rabbit<PERSON>, menganalisis data test, dan men<PERSON><PERSON><PERSON><PERSON> profil persona yang lengkap.

## Technology Stack

- **Node.js 20+** dengan ES Modules
- **RabbitMQ** untuk job queue
- **PostgreSQL** untuk database
- **Google Gemini API** untuk AI analysis
- **Docker** untuk containerization

## Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Setup
```bash
cp .env.example .env
# Edit .env dengan konfigurasi Anda
```

### 3. Required Environment Variables
```env
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=analysis_worker
DB_PASSWORD=your_password

# RabbitMQ
RABBITMQ_URL=amqp://localhost:5672
ANALYSIS_JOBS_QUEUE=analysis_jobs_queue
NOTIFICATION_EVENTS_QUEUE=notification_events

# Google Gemini
GEMINI_API_KEY=your_gemini_api_key
GEMINI_MODEL=gemini-1.5-flash

# Archive Service
ARCHIVE_SERVICE_URL=http://localhost:3001
```

### 4. Start Service
```bash
npm start
```

## Service Architecture

```
Assessment Service (Waiter) → Analysis Worker (Koki) → Archive Service (Storage)
```

Analysis Worker berperan sebagai **"Koki"** yang memproses data mentah menjadi analisis persona yang siap disajikan.

## Integrasi Antar Service

### 🔄 Alur Kerja Complete

**1. Assessment Service → Analysis Worker**
- Assessment Service membuat profile di Archive Service dengan status `pending`
- Kirim job ke RabbitMQ queue: `{"profileId": "uuid", "userId": "uuid"}`
- Return response ke user dengan profile ID

**2. Analysis Worker → Archive Service**
- Update status profile ke `processing`
- Ambil `raw_input_data` dari database
- Proses AI analysis
- Update hasil ke Archive Service
- Kirim notifikasi ke queue

**3. Archive Service → Analysis Worker**
- Menyediakan database access
- API endpoint untuk update status
- Menyimpan hasil analysis

## Data Contracts

### Input Data Format
```json
{
  "riasec": {
    "realistic": 85,
    "investigative": 92,
    "artistic": 45,
    "social": 78,
    "enterprising": 67,
    "conventional": 55
  },
  "ocean": {
    "openness": 78,
    "conscientiousness": 88,
    "extraversion": 65,
    "agreeableness": 82,
    "neuroticism": 35
  }
}
```

### Output Data Format
```json
{
  "personality_summary": {
    "title": "Creative Innovator",
    "summary_text": "Individu yang sangat analitis dan kreatif..."
  },
  "top_3_traits": ["Creative", "Analytical", "Empathetic"],
  "career_recommendations": [
    {
      "career": "UX Designer",
      "match_score": 92,
      "reason": "Keterampilan kreatif dan analitis yang kuat..."
    }
  ],
  "strengths_analysis": "Pola pikir analitis dikombinasikan...",
  "development_areas": "Pertimbangkan mengembangkan keterampilan..."
}
```

## Yang Diberikan ke Service Lain

### ✅ Ke Archive Service:
- **Status Updates**: `pending` → `processing` → `completed`/`failed`
- **Analysis Results**: Hasil analisis AI yang terstruktur
- **Error Messages**: Detail error jika analysis gagal
- **Timestamps**: Waktu mulai, selesai, atau gagal

### ✅ Ke Notification System:
- **Completion Events**: Notifikasi sukses dengan detail profile
- **Failure Events**: Notifikasi error dengan alasan kegagalan
- **Real-time Updates**: Event langsung saat job selesai

## Yang Diharapkan dari Service Lain

### 📥 Dari Assessment Service:
- **Profile Pre-creation**: Profile harus ada di Archive Service sebelum queue job
- **Complete Raw Data**: Data RIASEC dan OCEAN harus lengkap
- **Valid Job Messages**: Format JSON yang benar dengan `profileId` dan `userId`
- **Queue Setup**: RabbitMQ queues harus sudah dikonfigurasi

### 📥 Dari Archive Service:
- **Database Schema**:
  ```sql
  CREATE TABLE archive_schema.persona_profiles (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    raw_input_data JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    persona_result JSONB,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
  );
  ```

- **API Endpoints**:
  - `PATCH /internal/profiles/:profileId` - Update status dan hasil
  - `GET /health` - Health check

- **Database Access**: Read/write access ke `archive_schema.persona_profiles`

## Error Handling & Recovery

### Analysis Worker Handles:
- **Auto Retry**: Retry otomatis dengan exponential backoff
- **Invalid Data**: Graceful error handling dengan logging
- **Service Outages**: Queue message persistence dan retry logic
- **Resource Management**: Proper memory dan processing management

### Common Issues & Solutions

#### 1. Jobs Not Being Processed
```bash
# Check RabbitMQ queue
rabbitmqctl list_queues name messages

# Check Analysis Worker logs
docker logs analysis-worker
```

#### 2. Profile Not Found Errors
```bash
# Verify profile exists
psql -d atma_db -c "SELECT id, status FROM archive_schema.persona_profiles WHERE id = 'profile-uuid';"
```

#### 3. Archive Service Update Failures
```bash
# Test Archive Service connectivity
curl -X PATCH http://archive-service:3001/internal/profiles/test-id \
  -H "Content-Type: application/json" \
  -d '{"status": "processing"}'
```

## Docker Deployment

### Build & Run
```bash
docker build -t analysis-worker:latest .

docker run -d \
  --name analysis-worker \
  --env-file .env \
  --restart unless-stopped \
  analysis-worker:latest
```

### Docker Compose
```yaml
version: '3.8'
services:
  analysis-worker:
    build: .
    environment:
      - DB_HOST=postgres
      - RABBITMQ_URL=amqp://rabbitmq:5672
      - ARCHIVE_SERVICE_URL=http://archive-service:3001
    depends_on:
      - postgres
      - rabbitmq
    restart: unless-stopped
```

## Monitoring

### Key Metrics
- **Queue Depth**: Jumlah pending jobs
- **Processing Time**: Rata-rata waktu per job
- **Success Rate**: Persentase job yang berhasil
- **Error Rate**: Job yang gagal dan kategori error

### Health Checks
- Database connection validation
- RabbitMQ connection monitoring
- Graceful shutdown handling

### Log Format
```
🚀 Initializing Analysis Worker...
✅ Database connection successful
🔌 Connected to RabbitMQ successfully
🔄 Processing job for profile: abc-123
✅ Job completed successfully in 3247ms
📈 Stats: Processed=1, Success=1, Failed=0
```

## Integration Checklist

### For Assessment Service:
- [ ] Create profile in Archive Service before queuing job
- [ ] Include `profileId` and `userId` in job message
- [ ] Ensure complete RIASEC and OCEAN data
- [ ] Set up notification event consumption

### For Archive Service:
- [ ] Implement `PATCH /internal/profiles/:profileId` endpoint
- [ ] Support status transitions: `pending` → `processing` → `completed`/`failed`
- [ ] Store `persona_result` as JSONB
- [ ] Provide database access for Analysis Worker

### For System Admin:
- [ ] Configure RabbitMQ queues
- [ ] Set up PostgreSQL with proper schema
- [ ] Configure Google Gemini API access
- [ ] Set up monitoring and alerting

## Performance Tips

### For High-Volume Processing:
1. **Scale Workers**: Run multiple instances
2. **Database Optimization**: Add indexes on status, user_id
3. **RabbitMQ Tuning**: Adjust prefetch count
4. **Monitor Resources**: Track CPU, memory usage

### Database Indexes
```sql
CREATE INDEX idx_persona_profiles_status ON archive_schema.persona_profiles(status);
CREATE INDEX idx_persona_profiles_user_id ON archive_schema.persona_profiles(user_id);
```

---

**License**: ISC - ATMA Backend Team