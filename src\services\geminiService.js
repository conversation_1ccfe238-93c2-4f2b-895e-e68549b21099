import { GoogleGenAI, Type } from '@google/genai';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Initialize Google Gemini AI client
 */
const ai = new GoogleGenAI({
  apiKey: process.env.GEMINI_API_KEY
});

/**
 * Define the response schema for persona analysis
 * This schema ensures structured output from Gemini API
 */
const personaAnalysisSchema = {
  type: Type.OBJECT,
  properties: {
    personality_summary: {
      type: Type.OBJECT,
      properties: {
        title: { 
          type: Type.STRING,
          description: "A catchy title that summarizes the personality type"
        },
        summary_text: { 
          type: Type.STRING,
          description: "A comprehensive summary of the personality profile (200-300 words)"
        }
      },
      required: ["title", "summary_text"]
    },
    top_3_traits: {
      type: Type.ARRAY,
      items: { 
        type: Type.STRING,
        description: "Key personality traits"
      },
      minItems: 3,
      maxItems: 3,
      description: "The three most prominent personality traits"
    },
    career_recommendations: {
      type: Type.ARRAY,
      items: {
        type: Type.OBJECT,
        properties: {
          career: { 
            type: Type.STRING,
            description: "Specific career or job title"
          },
          match_score: { 
            type: Type.NUMBER,
            description: "Match percentage (0-100)"
          },
          reason: { 
            type: Type.STRING,
            description: "Explanation for why this career matches (100-150 words)"
          }
        },
        required: ["career", "match_score", "reason"]
      },
      minItems: 5,
      maxItems: 8,
      description: "Career recommendations based on personality analysis"
    },
    strengths_analysis: { 
      type: Type.STRING,
      description: "Detailed analysis of personality strengths (150-200 words)"
    },
    development_areas: { 
      type: Type.STRING,
      description: "Areas for personal development and growth (150-200 words)"
    },
    work_style: {
      type: Type.STRING,
      description: "Description of preferred work style and environment (100-150 words)"
    },
    motivators_and_values: {
      type: Type.STRING,
      description: "Key motivators, values, and what drives the individual (100-150 words)"
    },
    blind_spots: {
      type: Type.STRING,
      description: "Potential personality blind spots or risks (100-150 words)"
    },
    collaboration_tips: {
      type: Type.STRING,
      description: "Tips and best practices for working with this individual (100-150 words)"
    },
    similar_famous_personalities: {
      type: Type.ARRAY,
      items: { 
        type: Type.STRING,
        description: "Famous personality name"
      },
      description: "Examples of famous people with similar personality traits"
    }
  },
  required: [
    "personality_summary",
    "top_3_traits",
    "career_recommendations",
    "strengths_analysis",
    "development_areas",
    "work_style",
    "motivators_and_values",
    "blind_spots",
    "collaboration_tips"
  ],
  propertyOrdering: [
    "personality_summary",
    "top_3_traits",
    "career_recommendations",
    "strengths_analysis",
    "development_areas",
    "work_style",
    "motivators_and_values",
    "blind_spots",
    "collaboration_tips",
    "similar_famous_personalities"
  ]
};


/**
 * Create a comprehensive prompt for persona analysis
 * @param {Object} rawData - The raw input data containing RIASEC and OCEAN scores
 * @returns {string} The formatted prompt for Gemini API
 */
function createPersonaAnalysisPrompt(rawData) {
  const { riasec, ocean } = rawData;
  
  return `
Anda adalah seorang psikolog ahli dan konselor karir berpengalaman dengan spesialisasi dalam analisis psikometrik dan pengembangan karir. Tugas Anda adalah menganalisis data hasil tes psikometrik berikut untuk menghasilkan profil persona yang komprehensif, mendalam, dan actionable.

DATA HASIL TES PSIKOMETRIK:

RIASEC (Holland Codes) - Minat Karir:
- R (Realistic/Realistis): ${riasec.R} - Minat pada aktivitas praktis, hands-on, bekerja dengan alat/mesin
- I (Investigative/Investigatif): ${riasec.I} - Minat pada penelitian, analisis, pemecahan masalah kompleks
- A (Artistic/Artistik): ${riasec.A} - Minat pada kreativitas, ekspresi diri, seni
- S (Social/Sosial): ${riasec.S} - Minat pada interaksi sosial, membantu orang lain
- E (Enterprising/Enterprising): ${riasec.E} - Minat pada kepemimpinan, bisnis, persuasi
- C (Conventional/Konvensional): ${riasec.C} - Minat pada struktur, detail, administrasi

Big Five (OCEAN) - Kepribadian:
- O (Openness/Keterbukaan): ${ocean.O} - Keterbukaan terhadap pengalaman baru
- C (Conscientiousness/Kehati-hatian): ${ocean.C} - Kedisiplinan dan tanggung jawab
- E (Extraversion/Ekstraversi): ${ocean.E} - Orientasi sosial dan energi
- A (Agreeableness/Keramahan): ${ocean.A} - Kecenderungan kooperatif dan empati
- N (Neuroticism/Neurotisisme): ${ocean.N} - Stabilitas emosional (skor tinggi = kurang stabil)

INSTRUKSI ANALISIS:

1. Analisis kombinasi skor RIASEC dan OCEAN untuk memahami pola kepribadian yang unik
2. Identifikasi kekuatan utama dan area pengembangan berdasarkan profil lengkap
3. Berikan rekomendasi karir yang spesifik dengan reasoning yang kuat
4. Pastikan analisis bersifat konstruktif, mendorong pertumbuhan, dan actionable
5. Gunakan bahasa Indonesia yang profesional namun mudah dipahami

Hasilkan analisis yang mendalam dan personal berdasarkan kombinasi unik dari kedua framework psikometrik ini.
`;
}

/**
 * Analyze persona using Google Gemini AI
 * @param {Object} rawData - The raw input data from database
 * @returns {Promise<Object>} The structured persona analysis result
 */
export async function analyzePersona(rawData) {
  try {
    console.log('🤖 Starting Gemini AI persona analysis...');
    
    // Validate input data
    if (!rawData || !rawData.riasec || !rawData.ocean) {
      throw new Error('Invalid raw data: missing riasec or ocean data');
    }
    
    // Create the analysis prompt
    const prompt = createPersonaAnalysisPrompt(rawData);
    
    console.log('📝 Sending request to Gemini API...');
    
    // Call Gemini API with structured output
    const response = await ai.models.generateContent({
      model: process.env.GEMINI_MODEL || "gemini-1.5-flash",
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        responseSchema: personaAnalysisSchema,
        temperature: 0.7, // Balanced creativity and consistency
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 4000 // Ensure enough tokens for comprehensive analysis
      }
    });
    
    console.log('✅ Received response from Gemini API');
    
    // Parse the JSON response
    const analysisResult = JSON.parse(response.text);
    
    // Validate the response structure
    if (!analysisResult.personality_summary || !analysisResult.career_recommendations) {
      throw new Error('Invalid response structure from Gemini API');
    }
    
    console.log('🎯 Persona analysis completed successfully');
    console.log(`📊 Generated ${analysisResult.career_recommendations.length} career recommendations`);
    
    return analysisResult;
    
  } catch (error) {
    console.error('❌ Error in Gemini AI analysis:', error.message);
    
    // Re-throw with more context
    if (error.message.includes('API key')) {
      throw new Error('Gemini API key is invalid or missing');
    } else if (error.message.includes('quota')) {
      throw new Error('Gemini API quota exceeded');
    } else if (error.message.includes('JSON')) {
      throw new Error('Failed to parse Gemini API response as JSON');
    } else {
      throw new Error(`Gemini AI analysis failed: ${error.message}`);
    }
  }
}

/**
 * Test the Gemini AI service with sample data
 * @returns {Promise<boolean>} True if test is successful
 */
export async function testGeminiService() {
  try {
    console.log('🧪 Testing Gemini AI service...');
    
    const sampleData = {
      riasec: {
        "R": "85%",
        "I": "70%", 
        "A": "90%",
        "S": "40%",
        "E": "60%",
        "C": "55%"
      },
      ocean: {
        "O": "92%",
        "C": "75%",
        "E": "88%", 
        "A": "95%",
        "N": "15%"
      }
    };
    
    const result = await analyzePersona(sampleData);
    
    if (result && result.personality_summary && result.career_recommendations) {
      console.log('✅ Gemini AI service test successful');
      return true;
    } else {
      console.error('❌ Gemini AI service test failed: Invalid response structure');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Gemini AI service test failed:', error.message);
    return false;
  }
}
